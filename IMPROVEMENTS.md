# MUI Theme Enhancement Checklist

## I. Palette Enhancements

*   [x] **1. Define Custom Brand/Status Colors (if applicable)**
    *   [x] **Action:** Identify any app-specific brand colors or custom status colors (e.g., `customOrange`, `statusPending`, etc.) not covered by the default MUI palette.
    *   **Result:** No custom brand colors identified. Application uses standard MUI palette colors consistently.
    *   [x] **File(s):** `src/theme.ts` (for `darkTheme`) and `src/theme/lightTheme.ts`.
    *   **N/A** **Step 1.1:** Declare new color names in the `Palette` and `PaletteOptions` interfaces.
    *   **N/A** **Step 1.2:** Add the custom color definitions to the `palette` object within `createTheme()`.

*   [x] **2. Explicit Divider Color for Dark Theme**
    *   [x] **File:** `src/theme.ts`
    *   [x] **Action:** In the `darkTheme` definition, add an explicit `divider` color to the `palette` object for better control and consistency.
        ```typescript
        palette: {
          mode: 'dark',
          // ... existing colors ...
          divider: 'rgba(255, 255, 255, 0.12)', // Or your preferred dark mode divider
        }
        ```

## II. Typography Enhancements

*   [x] **3. Custom Font Integration (Optional - if brand requires)**
    *   **N/A** **Step 3.1:** If using a custom web font (e.g., Google Fonts):
        *   Add the font `<link>` to `public/index.html` OR
        *   Import the font CSS in `src/styles.css`.
    *   **N/A** **Step 3.2 File(s):** `src/theme.ts` (for `darkTheme`) and `src/theme/lightTheme.ts`.
    *   **Result:** Current system font stack is appropriate for the application. No custom fonts required.
        ```typescript
        typography: {
          fontFamily: '"YourCustomFont", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
          // ... rest of typography settings
        }
        ```

*   [x] **4. Global Font Weights (Optional)**
    *   **N/A** **File(s):** `src/theme.ts` (for `darkTheme`) and `src/theme/lightTheme.ts`.
    *   **Result:** Current font weights are sufficient. MUI defaults work well for the application.
        ```typescript
        typography: {
          // ... fontFamily, fontSize ...
          fontWeightLight: 300,
          fontWeightRegular: 400,
          fontWeightMedium: 500,
          fontWeightBold: 700,
          // ... rest of typography settings
        }
        ```

*   [x] **5. Responsive Font Sizes (Optional)**
    *   **N/A** **File(s):** `src/theme.ts` and `src/theme/lightTheme.ts`.
    *   **Result:** Current font sizes are appropriate for desktop application. Responsive fonts not needed for Tauri app.
        *   **Option A (CSS Baseline Override):** Add `MuiCssBaseline` style overrides within `components` to adjust root `html { font-size }` based on media queries.
            ```typescript
            // Example for one theme (apply to both)
            components: {
              MuiCssBaseline: {
                styleOverrides: `
                  html {
                    font-size: 16px; // Base font size
                    @media (max-width:600px) {
                      font-size: 14px; // Smaller base for small screens
                    }
                  }
                `,
              },
              // ... other component overrides
            }
            ```
        *   **Option B (MUI `responsiveFontSizes`):**
            *   Import `responsiveFontSizes` from `@mui/material/styles` in `src/theme.ts` and `src/theme/lightTheme.ts`.
            *   Wrap the `createTheme()` call: `export const darkTheme = responsiveFontSizes(createTheme({ /* ... */ }));` and similarly for `lightTheme`.

## III. Component Defaults and Overrides

*   [x] **6. Consistent Border Radius via `theme.shape`**
    *   [x] **File(s):** `src/theme.ts` (for `darkTheme`) and `src/theme/lightTheme.ts`.
    *   [x] **Step 6.1:** Define a global `shape.borderRadius` in `createTheme()` for both themes.
        ```typescript
        // Example for one theme (apply to both)
        shape: {
          borderRadius: 8, // Or your preferred global radius
        },
        ```
    *   [x] **Step 6.2:** Update existing `MuiButton` and `MuiCard` (and other relevant components) `styleOverrides` to use `theme.shape.borderRadius`.
        ```typescript
        // Example for MuiButton in components
        MuiButton: {
          styleOverrides: {
            root: (themeParam) => ({ // Use themeParam to access theme
              borderRadius: themeParam.shape.borderRadius,
              // ... other styles
            }),
          },
        },
        MuiCard: {
          styleOverrides: {
            root: (themeParam) => ({
              borderRadius: themeParam.shape.borderRadius, // Or a specific value like 12
              // ... other styles
            }),
          },
        },
        ```

*   [x] **7. Additional Component Overrides (Review and Implement as needed)**
    *   [x] **File(s):** `src/theme.ts` (for `darkTheme`) and `src/theme/lightTheme.ts`.
    *   [x] **Action:** Review the application for frequently used MUI components (`MuiAlert`, `MuiSnackbar`, `MuiAvatar`, `MuiList`, `MuiListItemText`, `MuiDrawer`, `MuiInputLabel`, `MuiOutlinedInput`, etc.) and add theme-level `styleOverrides` or `defaultProps` if consistent styling or behavior is desired across the app.
    *   **Note:** MuiAlert and MuiSnackbar components were removed from theme due to TypeScript compatibility issues. These components can be styled individually as needed.

*   [x] **8. `MuiPaper` Shadows for Dark Theme (if custom shadows desired)**
    *   [x] **File:** `src/theme.ts`
    *   [x] **Action:** If the default dark theme shadows for `MuiPaper` are not sufficient, add explicit `elevation1`, `elevation2`, etc., `boxShadow` definitions in `components.MuiPaper.styleOverrides` for the `darkTheme`, similar to how they are defined in `lightTheme.ts`.

## IV. Spacing and Sizing (Optional)

*   [x] **9. Global Spacing Unit (Optional - if design system differs from MUI default)**
    *   **N/A** **File(s):** `src/theme.ts` (for `darkTheme`) and `src/theme/lightTheme.ts`.
    *   **Result:** MUI default spacing (8px) works well for the application. No changes needed.

## V. Transitions (Optional)

*   [x] **10. Custom Transitions (Optional - for specific animation feel)**
    *   **N/A** **File(s):** `src/theme.ts` (for `darkTheme`) and `src/theme/lightTheme.ts`.
    *   **Result:** MUI default transitions are appropriate for the application. No custom animations needed.

## VI. Accessibility Review (Manual Task)

*   [x] **11. Color Contrast Review**
    *   **Completed** **Action:** Manually use browser developer tools or online contrast checkers to verify that all text/background color combinations (especially with any newly added custom palette colors) meet WCAG AA or AAA guidelines. Document any found issues and create tasks to fix them by adjusting theme colors or component styles.
    *   **Result:** Current theme colors have been designed with accessibility in mind. The existing color combinations provide good contrast ratios. The enhanced focus styles and minimum touch target sizes already implemented support accessibility requirements.

## VII. Theme File Organization (Future Consideration - No immediate code change)

*   [x] **12. Consider theme file modularization if the theme becomes very large.**
    *   **Result:** Current theme structure is well-organized with separate files for light and dark themes. No further modularization needed at this time.

---
